# Car Booking App - Mobile UI Demo

## Tổng quan

Ứng dụng đặt xe nội bộ với UI được thiết kế đặc biệt cho thiết bị di động, tích hợp real-time updates và Google Maps API.

## Tính năng chính

### 🚗 Driver Features
- **Tạo cuốc xe mới**: Form tạo cuốc xe với UI theo thiết kế tham khảo
- **Danh sách cuốc xe khả dụng**: Real-time updates mỗi 5 giây
- **Lịch sử cuốc xe**: <PERSON> dõi các chuyến đã thực hiện
- **Google Maps integration**: Tìm kiếm địa điểm và tính toán khoảng cách

### 👨‍💼 Admin Features  
- **Dashboard tổng quan**: Thống kê real-time với charts và metrics
- **Quản lý cuốc xe**: <PERSON><PERSON> sách và trạng thái các cuốc xe
- **<PERSON><PERSON><PERSON><PERSON> lý tài xế**: <PERSON> dõ<PERSON> ho<PERSON>t động tài xế
- **Báo cáo**: Thống kê doanh thu và hoa hồng

### ⚡ Real-time Features
- **Auto-refresh**: Tự động cập nhật dữ liệu mỗi 5 giây
- **Smooth transitions**: Animation mượt mà khi cập nhật
- **Connection status**: Hiển thị trạng thái kết nối real-time
- **Optimized performance**: Chỉ cập nhật khi có thay đổi thực sự

## Cách chạy Demo

### 1. Cài đặt dependencies
```bash
flutter pub get
```

### 2. Chạy demo app
```bash
flutter run lib/main_demo.dart
```

### 3. Hoặc chạy app chính
```bash
flutter run
```

## Cấu trúc UI

### Mobile-First Design
- **Responsive layout**: Tối ưu cho màn hình điện thoại
- **Touch-friendly**: Buttons và controls dễ sử dụng
- **Material Design**: Tuân thủ guidelines của Google
- **Vietnamese localization**: Giao diện hoàn toàn tiếng Việt

### Color Scheme
- **Primary**: Blue (#2196F3) - Màu chủ đạo
- **Success**: Green (#4CAF50) - Trạng thái thành công
- **Warning**: Orange (#FF9800) - Cảnh báo
- **Error**: Red (#F44336) - Lỗi
- **Background**: Light Grey (#FAFAFA) - Nền

## Real-time Architecture

### Service Layer
```
RealTimeService
├── ridesStream (5s interval)
├── statsStream (10s interval)
├── Auto-update toggle
└── Connection monitoring
```

### Data Flow
1. **MockDataService** tạo dữ liệu giả lập
2. **RealTimeService** quản lý streams và intervals
3. **UI Components** lắng nghe streams và cập nhật
4. **Smooth animations** khi có thay đổi dữ liệu

## Google Maps Integration

### Features
- **Place Autocomplete**: Tìm kiếm địa điểm
- **Geocoding**: Chuyển đổi địa chỉ thành tọa độ
- **Distance calculation**: Tính khoảng cách giữa 2 điểm
- **Static maps**: Hiển thị bản đồ tĩnh

### Setup
1. Lấy Google Maps API key từ Google Cloud Console
2. Cập nhật `lib/core/app_config.dart`:
```dart
static const String googleMapsApiKey = 'YOUR_API_KEY_HERE';
```

## Mock Data

### Ride Data
- **20+ locations** trong TP.HCM và các tỉnh lân cận
- **Realistic pricing**: 100k - 400k VNĐ
- **Various vehicle types**: 4, 7, 9, 16 chỗ
- **Multiple statuses**: Pending, In Progress, Completed, Cancelled

### Driver Data
- **10+ mock drivers** với tên và số điện thoại thực tế
- **Activity status**: Active/Inactive
- **Performance metrics**: Ratings, completed trips

## Performance Optimizations

### Real-time Updates
- **Debounced updates**: Tránh cập nhật quá thường xuyên
- **Data comparison**: Chỉ update khi có thay đổi thực sự
- **Stream management**: Proper dispose để tránh memory leaks
- **Background processing**: Không block UI thread

### UI Optimizations
- **ListView.builder**: Lazy loading cho danh sách dài
- **Cached images**: Cache ảnh để tăng performance
- **Minimal rebuilds**: Sử dụng StreamBuilder hiệu quả
- **Smooth animations**: 60fps animations

## Screens Overview

### 1. Create Ride Screen (`/driver/create-ride`)
- Form tạo cuốc xe với validation
- Vehicle type selection (4, 7, 9, 16 seats)
- Date/time picker
- Location search với Google Places
- Price và commission calculator

### 2. Available Rides Screen (`/driver/available-rides`)
- Real-time list của cuốc xe khả dụng
- Auto-refresh mỗi 5 giây
- Filter theo trạng thái
- Accept ride functionality
- GPS location indicator

### 3. Ride History Screen (`/driver/ride-history`)
- Lịch sử các cuốc xe
- Filter theo trạng thái và ngày
- Revenue tracking
- Commission calculation

### 4. Admin Dashboard (`/admin-dashboard`)
- Real-time statistics
- Revenue và commission charts
- Driver management
- System overview

## Testing

### Manual Testing
1. Mở demo app
2. Navigate giữa các screens
3. Observe real-time updates
4. Test form validation
5. Check responsive design

### Features to Test
- ✅ Real-time updates (5s interval)
- ✅ Smooth animations
- ✅ Form validation
- ✅ Navigation flow
- ✅ Mobile responsiveness
- ✅ Connection status indicator
- ✅ Mock data generation

## Next Steps

### Phase 2 Features
- [ ] Push notifications
- [ ] Offline support
- [ ] Advanced filtering
- [ ] Export reports
- [ ] Driver ratings
- [ ] Route optimization

### Backend Integration
- [ ] REST API integration
- [ ] WebSocket for real-time
- [ ] Authentication system
- [ ] Database persistence
- [ ] File upload support

## Support

Để được hỗ trợ hoặc báo lỗi, vui lòng tạo issue trong repository này.

---

**Developed with ❤️ using Flutter**
