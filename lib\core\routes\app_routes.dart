import 'package:flutter/material.dart';
import '../../features/splash/screens/splash_screen.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/admin/screens/admin_dashboard_screen.dart';
import '../../features/driver/screens/driver_dashboard_screen.dart';
import '../../features/driver/screens/create_ride_screen.dart';
import '../../features/driver/screens/available_rides_screen.dart';
import '../../features/driver/screens/ride_history_screen.dart';
import '../../features/booking/screens/create_booking_screen.dart';
import '../../features/booking/screens/booking_list_screen.dart';
import '../../features/booking/screens/booking_detail_screen.dart';
import '../../features/profile/screens/profile_screen.dart';

class AppRoutes {
  // Route names
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String adminDashboard = '/admin-dashboard';
  static const String driverDashboard = '/driver-dashboard';
  static const String createRide = '/driver/create-ride';
  static const String availableRides = '/driver/available-rides';
  static const String rideHistory = '/driver/ride-history';
  static const String createBooking = '/create-booking';
  static const String bookingList = '/booking-list';
  static const String bookingDetail = '/booking-detail';
  static const String profile = '/profile';
  
  // Routes map
  static Map<String, WidgetBuilder> get routes {
    return {
      splash: (context) => SplashScreen(),
      login: (context) => LoginScreen(),
      register: (context) => RegisterScreen(),
      adminDashboard: (context) => AdminDashboardScreen(),
      driverDashboard: (context) => DriverDashboardScreen(),
      createRide: (context) => CreateRideScreen(),
      availableRides: (context) => AvailableRidesScreen(),
      rideHistory: (context) => RideHistoryScreen(),
      createBooking: (context) => CreateBookingScreen(),
      bookingList: (context) => BookingListScreen(),
      bookingDetail: (context) => BookingDetailScreen(),
      profile: (context) => ProfileScreen(),
    };
  }
  
  // Navigation helpers
  static void pushNamed(BuildContext context, String routeName, {Object? arguments}) {
    Navigator.pushNamed(context, routeName, arguments: arguments);
  }
  
  static void pushReplacementNamed(BuildContext context, String routeName, {Object? arguments}) {
    Navigator.pushReplacementNamed(context, routeName, arguments: arguments);
  }
  
  static void pushNamedAndRemoveUntil(BuildContext context, String routeName, {Object? arguments}) {
    Navigator.pushNamedAndRemoveUntil(
      context, 
      routeName, 
      (route) => false,
      arguments: arguments,
    );
  }
  
  static void pop(BuildContext context, [Object? result]) {
    Navigator.pop(context, result);
  }
}
