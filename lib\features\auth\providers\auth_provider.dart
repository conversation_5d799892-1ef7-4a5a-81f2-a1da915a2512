import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../models/user_model.dart';
import '../../../core/app_config.dart';

class AuthProvider extends ChangeNotifier {
  UserModel? _currentUser;
  bool _isLoading = false;
  String? _error;
  bool _isLoggedIn = false;

  // Getters
  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _isLoggedIn;
  bool get isAdmin => _currentUser?.isAdmin ?? false;
  bool get isDriver => _currentUser?.isDriver ?? false;

  // Initialize auth state
  Future<void> initializeAuth() async {
    _setLoading(true);
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('user_id');
      final userJson = prefs.getString('user_data');
      
      if (userId != null && userJson != null) {
        // TODO: Validate token with server
        // For now, just restore user from local storage
        _currentUser = UserModel.fromJson({
          'id': userId,
          'email': '<EMAIL>',
          'fullName': 'Test User',
          'phoneNumber': '**********',
          'role': 'driver',
          'isActive': true,
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
        });
        _isLoggedIn = true;
      }
    } catch (e) {
      _setError('Lỗi khởi tạo xác thực: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Login
  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();
    
    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      // Mock login logic
      if (email.isNotEmpty && password.isNotEmpty) {
        final role = email.contains('admin') ? AppConfig.roleAdmin : AppConfig.roleDriver;
        
        _currentUser = UserModel(
          email: email,
          fullName: email.contains('admin') ? 'Admin User' : 'Driver User',
          phoneNumber: '**********',
          role: role,
          zaloId: 'zalo_${email.split('@')[0]}',
        );
        
        _isLoggedIn = true;
        
        // Save to local storage
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_id', _currentUser!.id);
        await prefs.setString('user_data', _currentUser!.toJson().toString());
        
        return true;
      } else {
        _setError('Email và mật khẩu không được để trống');
        return false;
      }
    } catch (e) {
      _setError('Lỗi đăng nhập: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Register
  Future<bool> register({
    required String email,
    required String password,
    required String fullName,
    required String phoneNumber,
    String? zaloId,
    String role = AppConfig.roleDriver,
  }) async {
    _setLoading(true);
    _clearError();
    
    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      // Mock registration logic
      if (email.isNotEmpty && password.isNotEmpty && fullName.isNotEmpty && phoneNumber.isNotEmpty) {
        _currentUser = UserModel(
          email: email,
          fullName: fullName,
          phoneNumber: phoneNumber,
          role: role,
          zaloId: zaloId,
        );
        
        _isLoggedIn = true;
        
        // Save to local storage
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('user_id', _currentUser!.id);
        await prefs.setString('user_data', _currentUser!.toJson().toString());
        
        return true;
      } else {
        _setError('Vui lòng điền đầy đủ thông tin');
        return false;
      }
    } catch (e) {
      _setError('Lỗi đăng ký: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Logout
  Future<void> logout() async {
    _setLoading(true);
    
    try {
      // Clear local storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('user_id');
      await prefs.remove('user_data');
      
      // Clear state
      _currentUser = null;
      _isLoggedIn = false;
      _clearError();
    } catch (e) {
      _setError('Lỗi đăng xuất: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Update user profile
  Future<bool> updateProfile({
    String? fullName,
    String? phoneNumber,
    String? zaloId,
    String? bankAccount,
    String? bankName,
  }) async {
    if (_currentUser == null) return false;
    
    _setLoading(true);
    _clearError();
    
    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      _currentUser = _currentUser!.copyWith(
        fullName: fullName,
        phoneNumber: phoneNumber,
        zaloId: zaloId,
        bankAccount: bankAccount,
        bankName: bankName,
      );
      
      // Update local storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_data', _currentUser!.toJson().toString());
      
      return true;
    } catch (e) {
      _setError('Lỗi cập nhật thông tin: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update user location
  Future<void> updateLocation(LocationModel location) async {
    if (_currentUser == null) return;
    
    try {
      _currentUser = _currentUser!.copyWith(currentLocation: location);
      notifyListeners();
      
      // TODO: Send location to server
    } catch (e) {
      _setError('Lỗi cập nhật vị trí: $e');
    }
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
