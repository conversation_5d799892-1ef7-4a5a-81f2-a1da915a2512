import 'package:flutter/foundation.dart';
import 'dart:async';
import '../../../models/booking_model.dart';
import '../../../models/user_model.dart';
import '../../../core/app_config.dart';

class BookingProvider extends ChangeNotifier {
  List<BookingModel> _bookings = [];
  List<BookingModel> _availableBookings = [];
  List<BookingModel> _myBookings = [];
  bool _isLoading = false;
  String? _error;
  Timer? _refreshTimer;

  // Getters
  List<BookingModel> get bookings => _bookings;
  List<BookingModel> get availableBookings => _availableBookings;
  List<BookingModel> get myBookings => _myBookings;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize booking provider
  void initialize() {
    startAutoRefresh();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  // Start auto refresh
  void startAutoRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(
      Duration(seconds: AppConfig.autoRefreshInterval),
      (_) => refreshBookings(),
    );
  }

  // Stop auto refresh
  void stopAutoRefresh() {
    _refreshTimer?.cancel();
  }

  // Create new booking
  Future<bool> createBooking({
    required String vehicleType,
    required DateTime pickupTime,
    required bool isImmediate,
    required double price,
    required double commissionRate,
    required String pickupAddress,
    required LocationModel pickupLocation,
    String? destinationAddress,
    LocationModel? destinationLocation,
    required String creatorZalo,
    required String customerZalo,
    String? customerName,
    String? customerPhone,
    String? notes,
    required String creatorId,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      final booking = BookingModel(
        creatorId: creatorId,
        vehicleType: vehicleType,
        pickupTime: pickupTime,
        isImmediate: isImmediate,
        price: price,
        commissionRate: commissionRate,
        pickupAddress: pickupAddress,
        pickupLocation: pickupLocation,
        destinationAddress: destinationAddress,
        destinationLocation: destinationLocation,
        creatorZalo: creatorZalo,
        customerZalo: customerZalo,
        customerName: customerName,
        customerPhone: customerPhone,
        notes: notes,
      );

      _bookings.add(booking);
      _availableBookings.add(booking);
      _myBookings.add(booking);

      notifyListeners();
      return true;
    } catch (e) {
      _setError('Lỗi tạo cuốc xe: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Accept booking
  Future<bool> acceptBooking(String bookingId, String driverId) async {
    _setLoading(true);
    _clearError();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      final bookingIndex = _bookings.indexWhere((b) => b.id == bookingId);
      if (bookingIndex != -1) {
        final booking = _bookings[bookingIndex];
        final updatedBooking = booking.copyWith(
          acceptedDriverId: driverId,
          status: AppConfig.statusAccepted,
        );

        _bookings[bookingIndex] = updatedBooking;
        
        // Remove from available bookings
        _availableBookings.removeWhere((b) => b.id == bookingId);
        
        // Add to my bookings if not already there
        final myBookingIndex = _myBookings.indexWhere((b) => b.id == bookingId);
        if (myBookingIndex != -1) {
          _myBookings[myBookingIndex] = updatedBooking;
        } else {
          _myBookings.add(updatedBooking);
        }

        notifyListeners();
        return true;
      }
      
      _setError('Không tìm thấy cuốc xe');
      return false;
    } catch (e) {
      _setError('Lỗi nhận cuốc xe: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Start trip
  Future<bool> startTrip(String bookingId) async {
    _setLoading(true);
    _clearError();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      final bookingIndex = _bookings.indexWhere((b) => b.id == bookingId);
      if (bookingIndex != -1) {
        final booking = _bookings[bookingIndex];
        final updatedBooking = booking.copyWith(
          status: AppConfig.statusInProgress,
        );

        _bookings[bookingIndex] = updatedBooking;
        
        // Update in my bookings
        final myBookingIndex = _myBookings.indexWhere((b) => b.id == bookingId);
        if (myBookingIndex != -1) {
          _myBookings[myBookingIndex] = updatedBooking;
        }

        notifyListeners();
        return true;
      }
      
      _setError('Không tìm thấy cuốc xe');
      return false;
    } catch (e) {
      _setError('Lỗi bắt đầu chuyến đi: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Complete trip
  Future<bool> completeTrip(String bookingId) async {
    _setLoading(true);
    _clearError();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      final bookingIndex = _bookings.indexWhere((b) => b.id == bookingId);
      if (bookingIndex != -1) {
        final booking = _bookings[bookingIndex];
        final updatedBooking = booking.copyWith(
          status: AppConfig.statusCompleted,
        );

        _bookings[bookingIndex] = updatedBooking;
        
        // Update in my bookings
        final myBookingIndex = _myBookings.indexWhere((b) => b.id == bookingId);
        if (myBookingIndex != -1) {
          _myBookings[myBookingIndex] = updatedBooking;
        }

        notifyListeners();
        return true;
      }
      
      _setError('Không tìm thấy cuốc xe');
      return false;
    } catch (e) {
      _setError('Lỗi hoàn thành chuyến đi: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Cancel booking
  Future<bool> cancelBooking(String bookingId, String reason) async {
    _setLoading(true);
    _clearError();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call

      final bookingIndex = _bookings.indexWhere((b) => b.id == bookingId);
      if (bookingIndex != -1) {
        final booking = _bookings[bookingIndex];
        final updatedBooking = booking.copyWith(
          status: AppConfig.statusCancelled,
          cancellationReason: reason,
        );

        _bookings[bookingIndex] = updatedBooking;
        
        // Remove from available bookings
        _availableBookings.removeWhere((b) => b.id == bookingId);
        
        // Update in my bookings
        final myBookingIndex = _myBookings.indexWhere((b) => b.id == bookingId);
        if (myBookingIndex != -1) {
          _myBookings[myBookingIndex] = updatedBooking;
        }

        notifyListeners();
        return true;
      }
      
      _setError('Không tìm thấy cuốc xe');
      return false;
    } catch (e) {
      _setError('Lỗi hủy cuốc xe: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Refresh bookings
  Future<void> refreshBookings() async {
    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate API call
      
      // Mock data - in real app, this would fetch from API
      // For now, just notify listeners to trigger UI refresh
      notifyListeners();
    } catch (e) {
      _setError('Lỗi làm mới danh sách cuốc xe: $e');
    }
  }

  // Load available bookings for driver
  Future<void> loadAvailableBookings(LocationModel? currentLocation) async {
    _setLoading(true);
    _clearError();

    try {
      // TODO: Implement actual API call with location filtering
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      // Mock data - filter by location and status
      _availableBookings = _bookings
          .where((booking) => booking.isPending)
          .toList();
      
      // Sort by distance if location is available
      if (currentLocation != null) {
        _availableBookings.sort((a, b) {
          // TODO: Calculate actual distance
          return a.pickupTime.compareTo(b.pickupTime);
        });
      }

      notifyListeners();
    } catch (e) {
      _setError('Lỗi tải danh sách cuốc xe: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load my bookings
  Future<void> loadMyBookings(String userId) async {
    _setLoading(true);
    _clearError();

    try {
      // TODO: Implement actual API call
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      _myBookings = _bookings
          .where((booking) => 
              booking.creatorId == userId || 
              booking.acceptedDriverId == userId)
          .toList();

      notifyListeners();
    } catch (e) {
      _setError('Lỗi tải lịch sử cuốc xe: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Get booking by ID
  BookingModel? getBookingById(String bookingId) {
    try {
      return _bookings.firstWhere((booking) => booking.id == bookingId);
    } catch (e) {
      return null;
    }
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
