import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/app_config.dart';
import '../../../core/routes/app_routes.dart';
import '../../../core/theme/app_theme.dart';
import '../../../models/user_model.dart';
import '../../auth/providers/auth_provider.dart';
import '../../booking/providers/booking_provider.dart';
import '../../location/providers/location_provider.dart';

class CreateBookingScreen extends StatefulWidget {
  const CreateBookingScreen({super.key});

  @override
  State<CreateBookingScreen> createState() => _CreateBookingScreenState();
}

class _CreateBookingScreenState extends State<CreateBookingScreen> {
  final _formKey = GlobalKey<FormState>();
  final _priceController = TextEditingController();
  final _commissionController = TextEditingController(text: '10');
  final _pickupAddressController = TextEditingController();
  final _destinationAddressController = TextEditingController();
  final _creatorZaloController = TextEditingController();
  final _customerZaloController = TextEditingController();
  final _customerNameController = TextEditingController();
  final _customerPhoneController = TextEditingController();
  final _notesController = TextEditingController();

  String _selectedVehicleType = AppConfig.vehicleTypes.first;
  DateTime _selectedDateTime = DateTime.now().add(const Duration(hours: 1));
  bool _isImmediate = false;
  LocationModel? _pickupLocation;
  LocationModel? _destinationLocation;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    final authProvider = context.read<AuthProvider>();
    final user = authProvider.currentUser;
    
    if (user != null) {
      _creatorZaloController.text = user.zaloId ?? '';
    }
  }

  @override
  void dispose() {
    _priceController.dispose();
    _commissionController.dispose();
    _pickupAddressController.dispose();
    _destinationAddressController.dispose();
    _creatorZaloController.dispose();
    _customerZaloController.dispose();
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _selectDateTime() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDateTime,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );

    if (date != null && mounted) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_selectedDateTime),
      );

      if (time != null) {
        setState(() {
          _selectedDateTime = DateTime(
            date.year,
            date.month,
            date.day,
            time.hour,
            time.minute,
          );
        });
      }
    }
  }

  Future<void> _selectPickupLocation() async {
    final locationProvider = context.read<LocationProvider>();
    
    if (_pickupAddressController.text.isNotEmpty) {
      final location = await locationProvider.getCoordinatesFromAddress(
        _pickupAddressController.text,
      );
      
      if (location != null) {
        setState(() {
          _pickupLocation = location;
        });
      }
    }
  }

  Future<void> _createBooking() async {
    if (!_formKey.currentState!.validate()) return;

    if (_pickupLocation == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng chọn địa điểm đón'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    final authProvider = context.read<AuthProvider>();
    final bookingProvider = context.read<BookingProvider>();
    
    final success = await bookingProvider.createBooking(
      vehicleType: _selectedVehicleType,
      pickupTime: _isImmediate 
          ? DateTime.now().add(const Duration(minutes: 15))
          : _selectedDateTime,
      isImmediate: _isImmediate,
      price: double.parse(_priceController.text),
      commissionRate: double.parse(_commissionController.text) / 100,
      pickupAddress: _pickupAddressController.text,
      pickupLocation: _pickupLocation!,
      destinationAddress: _destinationAddressController.text.isNotEmpty 
          ? _destinationAddressController.text 
          : null,
      destinationLocation: _destinationLocation,
      creatorZalo: _creatorZaloController.text,
      customerZalo: _customerZaloController.text,
      customerName: _customerNameController.text.isNotEmpty 
          ? _customerNameController.text 
          : null,
      customerPhone: _customerPhoneController.text.isNotEmpty 
          ? _customerPhoneController.text 
          : null,
      notes: _notesController.text.isNotEmpty 
          ? _notesController.text 
          : null,
      creatorId: authProvider.currentUser!.id,
    );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Tạo cuốc xe thành công!'),
          backgroundColor: AppTheme.successColor,
        ),
      );
      AppRoutes.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tạo cuốc xe mới'),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Vehicle Type
              const Text(
                'Loại xe *',
                style: AppTheme.headingSmall,
              ),
              const SizedBox(height: 8),
              DropdownButtonFormField<String>(
                value: _selectedVehicleType,
                decoration: const InputDecoration(
                  hintText: 'Chọn loại xe',
                ),
                items: AppConfig.vehicleTypes.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedVehicleType = value;
                    });
                  }
                },
              ),
              
              const SizedBox(height: 20),
              
              // Immediate pickup option
              CheckboxListTile(
                title: const Text('Đi ngay càng sớm càng tốt'),
                subtitle: const Text('Hệ thống sẽ gán trong 10-20 phút'),
                value: _isImmediate,
                onChanged: (value) {
                  setState(() {
                    _isImmediate = value ?? false;
                  });
                },
              ),
              
              // Pickup time (if not immediate)
              if (!_isImmediate) ...[
                const SizedBox(height: 16),
                const Text(
                  'Thời gian đón *',
                  style: AppTheme.headingSmall,
                ),
                const SizedBox(height: 8),
                InkWell(
                  onTap: _selectDateTime,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.access_time),
                        const SizedBox(width: 12),
                        Text(
                          '${_selectedDateTime.day}/${_selectedDateTime.month}/${_selectedDateTime.year} - ${_selectedDateTime.hour.toString().padLeft(2, '0')}:${_selectedDateTime.minute.toString().padLeft(2, '0')}',
                          style: AppTheme.bodyMedium,
                        ),
                        const Spacer(),
                        const Icon(Icons.arrow_drop_down),
                      ],
                    ),
                  ),
                ),
              ],
              
              const SizedBox(height: 20),
              
              // Price
              TextFormField(
                controller: _priceController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Giá cuốc xe (VNĐ) *',
                  hintText: 'Nhập giá cuốc xe',
                  prefixIcon: Icon(Icons.attach_money),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Vui lòng nhập giá cuốc xe';
                  }
                  if (double.tryParse(value) == null || double.parse(value) <= 0) {
                    return 'Giá cuốc xe không hợp lệ';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Commission rate
              TextFormField(
                controller: _commissionController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Tỷ lệ hoa hồng (%) *',
                  hintText: 'Nhập tỷ lệ hoa hồng cho người tạo',
                  prefixIcon: Icon(Icons.percent),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Vui lòng nhập tỷ lệ hoa hồng';
                  }
                  final rate = double.tryParse(value);
                  if (rate == null || rate < 0 || rate > 100) {
                    return 'Tỷ lệ hoa hồng phải từ 0-100%';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 20),
              
              // Pickup address
              TextFormField(
                controller: _pickupAddressController,
                decoration: const InputDecoration(
                  labelText: 'Địa chỉ đón khách *',
                  hintText: 'Nhập địa chỉ đón khách',
                  prefixIcon: Icon(Icons.location_on),
                ),
                onChanged: (value) {
                  if (value.isNotEmpty) {
                    _selectPickupLocation();
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Vui lòng nhập địa chỉ đón khách';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Destination address (optional)
              TextFormField(
                controller: _destinationAddressController,
                decoration: const InputDecoration(
                  labelText: 'Địa chỉ đến (tùy chọn)',
                  hintText: 'Nhập địa chỉ đến',
                  prefixIcon: Icon(Icons.flag),
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Contact information
              const Text(
                'Thông tin liên hệ',
                style: AppTheme.headingSmall,
              ),
              const SizedBox(height: 12),
              
              TextFormField(
                controller: _creatorZaloController,
                decoration: const InputDecoration(
                  labelText: 'Zalo của chủ cuốc xe *',
                  hintText: 'Nhập Zalo ID',
                  prefixIcon: Icon(Icons.chat),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Vui lòng nhập Zalo của chủ cuốc xe';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _customerZaloController,
                decoration: const InputDecoration(
                  labelText: 'Zalo của khách hàng *',
                  hintText: 'Nhập Zalo ID khách hàng',
                  prefixIcon: Icon(Icons.person),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Vui lòng nhập Zalo của khách hàng';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _customerNameController,
                decoration: const InputDecoration(
                  labelText: 'Tên khách hàng (tùy chọn)',
                  hintText: 'Nhập tên khách hàng',
                  prefixIcon: Icon(Icons.person_outline),
                ),
              ),
              
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _customerPhoneController,
                keyboardType: TextInputType.phone,
                decoration: const InputDecoration(
                  labelText: 'Số điện thoại khách hàng (tùy chọn)',
                  hintText: 'Nhập số điện thoại',
                  prefixIcon: Icon(Icons.phone),
                ),
              ),
              
              const SizedBox(height: 16),
              
              TextFormField(
                controller: _notesController,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'Ghi chú (tùy chọn)',
                  hintText: 'Nhập ghi chú thêm',
                  prefixIcon: Icon(Icons.note),
                ),
              ),
              
              const SizedBox(height: 30),
              
              // Create button
              Consumer<BookingProvider>(
                builder: (context, bookingProvider, child) {
                  return SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: bookingProvider.isLoading ? null : _createBooking,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: bookingProvider.isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : const Text(
                              'TẠO CUỐC XE',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  );
                },
              ),
              
              const SizedBox(height: 20),
              
              // Error message
              Consumer<BookingProvider>(
                builder: (context, bookingProvider, child) {
                  if (bookingProvider.error != null) {
                    return Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppTheme.errorColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: AppTheme.errorColor.withOpacity(0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: AppTheme.errorColor,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              bookingProvider.error!,
                              style: TextStyle(
                                color: AppTheme.errorColor,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
