import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import '../../../models/user_model.dart';

class LocationProvider extends ChangeNotifier {
  LocationModel? _currentLocation;
  bool _isLoading = false;
  String? _error;
  bool _isLocationServiceEnabled = false;
  bool _hasLocationPermission = false;

  // Getters
  LocationModel? get currentLocation => _currentLocation;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLocationServiceEnabled => _isLocationServiceEnabled;
  bool get hasLocationPermission => _hasLocationPermission;

  // Initialize location services
  Future<void> initialize() async {
    await checkLocationPermission();
    await checkLocationService();
    if (_hasLocationPermission && _isLocationServiceEnabled) {
      await getCurrentLocation();
    }
  }

  // Check location permission
  Future<void> checkLocationPermission() async {
    try {
      final status = await Permission.location.status;
      _hasLocationPermission = status.isGranted;
      notifyListeners();
    } catch (e) {
      _setError('Lỗi kiểm tra quyền vị trí: $e');
    }
  }

  // Request location permission
  Future<bool> requestLocationPermission() async {
    _setLoading(true);
    _clearError();

    try {
      final status = await Permission.location.request();
      _hasLocationPermission = status.isGranted;
      
      if (!_hasLocationPermission) {
        _setError('Cần cấp quyền truy cập vị trí để sử dụng ứng dụng');
      }
      
      notifyListeners();
      return _hasLocationPermission;
    } catch (e) {
      _setError('Lỗi yêu cầu quyền vị trí: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Check if location service is enabled
  Future<void> checkLocationService() async {
    try {
      _isLocationServiceEnabled = await Geolocator.isLocationServiceEnabled();
      notifyListeners();
    } catch (e) {
      _setError('Lỗi kiểm tra dịch vụ vị trí: $e');
    }
  }

  // Get current location
  Future<LocationModel?> getCurrentLocation() async {
    if (!_hasLocationPermission) {
      final granted = await requestLocationPermission();
      if (!granted) return null;
    }

    if (!_isLocationServiceEnabled) {
      _setError('Dịch vụ vị trí chưa được bật');
      return null;
    }

    _setLoading(true);
    _clearError();

    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      _currentLocation = LocationModel(
        latitude: position.latitude,
        longitude: position.longitude,
        timestamp: DateTime.now(),
      );

      // Get address from coordinates
      await _getAddressFromCoordinates(_currentLocation!);

      notifyListeners();
      return _currentLocation;
    } catch (e) {
      _setError('Lỗi lấy vị trí hiện tại: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  // Watch location changes
  Stream<LocationModel> watchLocation() async* {
    if (!_hasLocationPermission || !_isLocationServiceEnabled) {
      throw Exception('Không có quyền hoặc dịch vụ vị trí chưa được bật');
    }

    const locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10, // Update every 10 meters
    );

    await for (final position in Geolocator.getPositionStream(locationSettings: locationSettings)) {
      final location = LocationModel(
        latitude: position.latitude,
        longitude: position.longitude,
        timestamp: DateTime.now(),
      );

      // Get address from coordinates
      await _getAddressFromCoordinates(location);

      _currentLocation = location;
      notifyListeners();
      yield location;
    }
  }

  // Calculate distance between two locations
  double calculateDistance(LocationModel from, LocationModel to) {
    return Geolocator.distanceBetween(
      from.latitude,
      from.longitude,
      to.latitude,
      to.longitude,
    ) / 1000; // Convert to kilometers
  }

  // Get address from coordinates (reverse geocoding)
  Future<void> _getAddressFromCoordinates(LocationModel location) async {
    try {
      // TODO: Implement reverse geocoding using Google Maps API or other service
      // For now, just set mock address
      final updatedLocation = LocationModel(
        latitude: location.latitude,
        longitude: location.longitude,
        address: 'Địa chỉ mẫu, Phường/Xã, Quận/Huyện, Tỉnh/Thành phố',
        province: 'Hồ Chí Minh',
        district: 'Quận 1',
        ward: 'Phường Bến Nghé',
        timestamp: location.timestamp,
      );
      
      if (_currentLocation?.latitude == location.latitude && 
          _currentLocation?.longitude == location.longitude) {
        _currentLocation = updatedLocation;
      }
    } catch (e) {
      // Ignore geocoding errors, keep location without address
      debugPrint('Lỗi geocoding: $e');
    }
  }

  // Get coordinates from address (geocoding)
  Future<LocationModel?> getCoordinatesFromAddress(String address) async {
    _setLoading(true);
    _clearError();

    try {
      // TODO: Implement geocoding using Google Maps API or other service
      // For now, return mock coordinates
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      final location = LocationModel(
        latitude: 10.762622, // Ho Chi Minh City coordinates
        longitude: 106.660172,
        address: address,
        province: 'Hồ Chí Minh',
        district: 'Quận 1',
        ward: 'Phường Bến Nghé',
      );

      return location;
    } catch (e) {
      _setError('Lỗi tìm tọa độ từ địa chỉ: $e');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  // Sort locations by distance from current location
  List<T> sortByDistance<T>(
    List<T> items,
    LocationModel Function(T) getLocation,
  ) {
    if (_currentLocation == null) return items;

    final sortedItems = List<T>.from(items);
    sortedItems.sort((a, b) {
      final distanceA = calculateDistance(_currentLocation!, getLocation(a));
      final distanceB = calculateDistance(_currentLocation!, getLocation(b));
      return distanceA.compareTo(distanceB);
    });

    return sortedItems;
  }

  // Filter locations within radius
  List<T> filterByRadius<T>(
    List<T> items,
    LocationModel Function(T) getLocation,
    double radiusKm,
  ) {
    if (_currentLocation == null) return items;

    return items.where((item) {
      final distance = calculateDistance(_currentLocation!, getLocation(item));
      return distance <= radiusKm;
    }).toList();
  }

  // Open location settings
  Future<void> openLocationSettings() async {
    try {
      await Geolocator.openLocationSettings();
    } catch (e) {
      _setError('Lỗi mở cài đặt vị trí: $e');
    }
  }

  // Open app settings
  Future<void> openAppSettings() async {
    try {
      await openAppSettings();
    } catch (e) {
      _setError('Lỗi mở cài đặt ứng dụng: $e');
    }
  }

  // Private methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
