import 'package:flutter/material.dart';
import 'features/driver/screens/available_rides_screen.dart';
import 'features/driver/screens/create_ride_screen.dart';
import 'features/driver/screens/ride_history_screen.dart';
import 'features/admin/screens/admin_dashboard_screen.dart';
import 'features/profile/screens/profile_screen.dart';
import 'features/profile/screens/edit_profile_screen.dart';
import 'features/auth/screens/login_screen.dart';
import 'features/auth/screens/register_screen.dart';

void main() {
  runApp(CarBookingApp());
}

class CarBookingApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Cuốc xe',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Roboto',
      ),
      debugShowCheckedModeBanner: false,
      initialRoute: '/login',
      routes: {
        '/login': (context) => LoginScreen(),
        '/register': (context) => RegisterScreen(),
        '/driver/available-rides': (context) => AvailableRidesScreen(),
        '/driver/create-ride': (context) => CreateRideScreen(),
        '/driver/ride-history': (context) => RideHistoryScreen(),
        '/admin/dashboard': (context) => AdminDashboardScreen(),
        '/profile': (context) => ProfileScreen(),
        '/edit-profile': (context) => EditProfileScreen(),
      },
    );
  }
}
