import 'package:uuid/uuid.dart';
import 'user_model.dart';

class BookingModel {
  final String id;
  final String creatorId;
  final String? acceptedDriverId;
  final String vehicleType; // '4 chỗ', '7 chỗ', '9 chỗ', '16 chỗ'
  final DateTime pickupTime;
  final bool isImmediate; // "Đi ngay càng sớm càng tốt"
  final double price;
  final double commissionRate; // Tỷ lệ hoa hồng cho người tạo
  final double commissionAmount; // Số tiền hoa hồng thực tế
  final String pickupAddress;
  final LocationModel pickupLocation;
  final String? destinationAddress;
  final LocationModel? destinationLocation;
  final String creatorZalo;
  final String customerZalo;
  final String? customerName;
  final String? customerPhone;
  final String status; // pending, accepted, in_progress, completed, cancelled
  final String? notes;
  final String? cancellationReason;
  final DateTime createdAt;
  final DateTime updatedAt;
  final UserModel? creator;
  final UserModel? acceptedDriver;
  final List<BookingHistoryModel> history;

  BookingModel({
    String? id,
    required this.creatorId,
    this.acceptedDriverId,
    required this.vehicleType,
    required this.pickupTime,
    this.isImmediate = false,
    required this.price,
    required this.commissionRate,
    double? commissionAmount,
    required this.pickupAddress,
    required this.pickupLocation,
    this.destinationAddress,
    this.destinationLocation,
    required this.creatorZalo,
    required this.customerZalo,
    this.customerName,
    this.customerPhone,
    this.status = 'pending',
    this.notes,
    this.cancellationReason,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.creator,
    this.acceptedDriver,
    List<BookingHistoryModel>? history,
  })  : id = id ?? const Uuid().v4(),
        commissionAmount = commissionAmount ?? (price * commissionRate),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now(),
        history = history ?? [];

  factory BookingModel.fromJson(Map<String, dynamic> json) {
    return BookingModel(
      id: json['id'],
      creatorId: json['creatorId'],
      acceptedDriverId: json['acceptedDriverId'],
      vehicleType: json['vehicleType'],
      pickupTime: DateTime.parse(json['pickupTime']),
      isImmediate: json['isImmediate'] ?? false,
      price: json['price'].toDouble(),
      commissionRate: json['commissionRate'].toDouble(),
      commissionAmount: json['commissionAmount'].toDouble(),
      pickupAddress: json['pickupAddress'],
      pickupLocation: LocationModel.fromJson(json['pickupLocation']),
      destinationAddress: json['destinationAddress'],
      destinationLocation: json['destinationLocation'] != null
          ? LocationModel.fromJson(json['destinationLocation'])
          : null,
      creatorZalo: json['creatorZalo'],
      customerZalo: json['customerZalo'],
      customerName: json['customerName'],
      customerPhone: json['customerPhone'],
      status: json['status'],
      notes: json['notes'],
      cancellationReason: json['cancellationReason'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      creator: json['creator'] != null
          ? UserModel.fromJson(json['creator'])
          : null,
      acceptedDriver: json['acceptedDriver'] != null
          ? UserModel.fromJson(json['acceptedDriver'])
          : null,
      history: json['history'] != null
          ? (json['history'] as List)
              .map((h) => BookingHistoryModel.fromJson(h))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'creatorId': creatorId,
      'acceptedDriverId': acceptedDriverId,
      'vehicleType': vehicleType,
      'pickupTime': pickupTime.toIso8601String(),
      'isImmediate': isImmediate,
      'price': price,
      'commissionRate': commissionRate,
      'commissionAmount': commissionAmount,
      'pickupAddress': pickupAddress,
      'pickupLocation': pickupLocation.toJson(),
      'destinationAddress': destinationAddress,
      'destinationLocation': destinationLocation?.toJson(),
      'creatorZalo': creatorZalo,
      'customerZalo': customerZalo,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'status': status,
      'notes': notes,
      'cancellationReason': cancellationReason,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'creator': creator?.toJson(),
      'acceptedDriver': acceptedDriver?.toJson(),
      'history': history.map((h) => h.toJson()).toList(),
    };
  }

  BookingModel copyWith({
    String? acceptedDriverId,
    String? status,
    String? notes,
    String? cancellationReason,
    DateTime? updatedAt,
    UserModel? acceptedDriver,
    List<BookingHistoryModel>? history,
  }) {
    return BookingModel(
      id: id,
      creatorId: creatorId,
      acceptedDriverId: acceptedDriverId ?? this.acceptedDriverId,
      vehicleType: vehicleType,
      pickupTime: pickupTime,
      isImmediate: isImmediate,
      price: price,
      commissionRate: commissionRate,
      commissionAmount: commissionAmount,
      pickupAddress: pickupAddress,
      pickupLocation: pickupLocation,
      destinationAddress: destinationAddress,
      destinationLocation: destinationLocation,
      creatorZalo: creatorZalo,
      customerZalo: customerZalo,
      customerName: customerName,
      customerPhone: customerPhone,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      creator: creator,
      acceptedDriver: acceptedDriver ?? this.acceptedDriver,
      history: history ?? this.history,
    );
  }

  bool get isPending => status == 'pending';
  bool get isAccepted => status == 'accepted';
  bool get isInProgress => status == 'in_progress';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';
  
  bool get canBeAccepted => isPending;
  bool get canBeStarted => isAccepted;
  bool get canBeCompleted => isInProgress;
  bool get canBeCancelled => isPending || isAccepted;
  
  Duration get timeUntilPickup => pickupTime.difference(DateTime.now());
  bool get isOverdue => DateTime.now().isAfter(pickupTime) && !isCompleted && !isCancelled;
}

class BookingHistoryModel {
  final String id;
  final String bookingId;
  final String action; // created, accepted, started, completed, cancelled
  final String? userId;
  final String? userName;
  final String? notes;
  final DateTime timestamp;

  BookingHistoryModel({
    String? id,
    required this.bookingId,
    required this.action,
    this.userId,
    this.userName,
    this.notes,
    DateTime? timestamp,
  })  : id = id ?? const Uuid().v4(),
        timestamp = timestamp ?? DateTime.now();

  factory BookingHistoryModel.fromJson(Map<String, dynamic> json) {
    return BookingHistoryModel(
      id: json['id'],
      bookingId: json['bookingId'],
      action: json['action'],
      userId: json['userId'],
      userName: json['userName'],
      notes: json['notes'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bookingId': bookingId,
      'action': action,
      'userId': userId,
      'userName': userName,
      'notes': notes,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}
