class Ride {
  final String id;
  final String driverId;
  final String driverName;
  final String driverPhone;
  final String fromLocation;
  final String toLocation;
  final double? fromLatitude;
  final double? fromLongitude;
  final double? toLatitude;
  final double? toLongitude;
  final DateTime departureTime;
  final int totalSeats;
  final int availableSeats;
  final double pricePerSeat;
  final double commission;
  final RideStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? pickupAddress;
  final String? dropoffAddress;
  final Duration? estimatedDuration;
  final String? notes;

  Ride({
    required this.id,
    required this.driverId,
    required this.driverName,
    required this.driverPhone,
    required this.fromLocation,
    required this.toLocation,
    this.fromLatitude,
    this.fromLongitude,
    this.toLatitude,
    this.toLongitude,
    required this.departureTime,
    required this.totalSeats,
    required this.availableSeats,
    required this.pricePerSeat,
    required this.commission,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.pickupAddress,
    this.dropoffAddress,
    this.estimatedDuration,
    this.notes,
  });

  factory Ride.fromJson(Map<String, dynamic> json) {
    return Ride(
      id: json['id'],
      driverId: json['driverId'],
      driverName: json['driverName'],
      driverPhone: json['driverPhone'],
      fromLocation: json['fromLocation'],
      toLocation: json['toLocation'],
      fromLatitude: json['fromLatitude']?.toDouble(),
      fromLongitude: json['fromLongitude']?.toDouble(),
      toLatitude: json['toLatitude']?.toDouble(),
      toLongitude: json['toLongitude']?.toDouble(),
      departureTime: DateTime.parse(json['departureTime']),
      totalSeats: json['totalSeats'],
      availableSeats: json['availableSeats'],
      pricePerSeat: json['pricePerSeat'].toDouble(),
      commission: json['commission'].toDouble(),
      status: RideStatus.values.firstWhere(
        (e) => e.toString() == 'RideStatus.${json['status']}',
        orElse: () => RideStatus.pending,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      pickupAddress: json['pickupAddress'],
      dropoffAddress: json['dropoffAddress'],
      estimatedDuration: json['estimatedDuration'] != null
          ? Duration(minutes: json['estimatedDuration'])
          : null,
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'driverId': driverId,
      'driverName': driverName,
      'driverPhone': driverPhone,
      'fromLocation': fromLocation,
      'toLocation': toLocation,
      'fromLatitude': fromLatitude,
      'fromLongitude': fromLongitude,
      'toLatitude': toLatitude,
      'toLongitude': toLongitude,
      'departureTime': departureTime.toIso8601String(),
      'totalSeats': totalSeats,
      'availableSeats': availableSeats,
      'pricePerSeat': pricePerSeat,
      'commission': commission,
      'status': status.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'pickupAddress': pickupAddress,
      'dropoffAddress': dropoffAddress,
      'estimatedDuration': estimatedDuration?.inMinutes,
      'notes': notes,
    };
  }

  Ride copyWith({
    String? id,
    String? driverId,
    String? driverName,
    String? driverPhone,
    String? fromLocation,
    String? toLocation,
    double? fromLatitude,
    double? fromLongitude,
    double? toLatitude,
    double? toLongitude,
    DateTime? departureTime,
    int? totalSeats,
    int? availableSeats,
    double? pricePerSeat,
    double? commission,
    RideStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? pickupAddress,
    String? dropoffAddress,
    Duration? estimatedDuration,
    String? notes,
  }) {
    return Ride(
      id: id ?? this.id,
      driverId: driverId ?? this.driverId,
      driverName: driverName ?? this.driverName,
      driverPhone: driverPhone ?? this.driverPhone,
      fromLocation: fromLocation ?? this.fromLocation,
      toLocation: toLocation ?? this.toLocation,
      fromLatitude: fromLatitude ?? this.fromLatitude,
      fromLongitude: fromLongitude ?? this.fromLongitude,
      toLatitude: toLatitude ?? this.toLatitude,
      toLongitude: toLongitude ?? this.toLongitude,
      departureTime: departureTime ?? this.departureTime,
      totalSeats: totalSeats ?? this.totalSeats,
      availableSeats: availableSeats ?? this.availableSeats,
      pricePerSeat: pricePerSeat ?? this.pricePerSeat,
      commission: commission ?? this.commission,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      pickupAddress: pickupAddress ?? this.pickupAddress,
      dropoffAddress: dropoffAddress ?? this.dropoffAddress,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      notes: notes ?? this.notes,
    );
  }

  @override
  String toString() {
    return 'Ride(id: $id, from: $fromLocation, to: $toLocation, departure: $departureTime, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Ride && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum RideStatus {
  pending,
  available,
  inProgress,
  completed,
  cancelled,
}

extension RideStatusExtension on RideStatus {
  String get displayName {
    switch (this) {
      case RideStatus.pending:
        return 'Đang chờ';
      case RideStatus.available:
        return 'Khả dụng';
      case RideStatus.inProgress:
        return 'Đang thực hiện';
      case RideStatus.completed:
        return 'Hoàn thành';
      case RideStatus.cancelled:
        return 'Đã hủy';
    }
  }

  String get colorCode {
    switch (this) {
      case RideStatus.pending:
        return '#FFA726';
      case RideStatus.available:
        return '#66BB6A';
      case RideStatus.inProgress:
        return '#42A5F5';
      case RideStatus.completed:
        return '#4CAF50';
      case RideStatus.cancelled:
        return '#EF5350';
    }
  }
}
