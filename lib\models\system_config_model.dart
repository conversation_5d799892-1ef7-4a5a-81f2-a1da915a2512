import 'package:uuid/uuid.dart';
import 'user_model.dart';
import 'booking_model.dart';

class SystemConfigModel {
  final String id;
  final double defaultCommissionRate;
  final double cancelPenaltyAmount;
  final double noShowPenaltyAmount;
  final double latePickupPenaltyAmount;
  final int autoRefreshInterval; // seconds
  final double maxDistanceKm;
  final int maxBookingHours;
  final bool enableAutoAssignment;
  final bool enablePenalties;
  final bool enableCommissions;
  final DateTime updatedAt;
  final String updatedBy;

  SystemConfigModel({
    String? id,
    this.defaultCommissionRate = 0.1,
    this.cancelPenaltyAmount = 50000,
    this.noShowPenaltyAmount = 100000,
    this.latePickupPenaltyAmount = 25000,
    this.autoRefreshInterval = 5,
    this.maxDistanceKm = 50.0,
    this.maxBookingHours = 24,
    this.enableAutoAssignment = true,
    this.enablePenalties = true,
    this.enableCommissions = true,
    DateTime? updatedAt,
    required this.updatedBy,
  })  : id = id ?? const Uuid().v4(),
        updatedAt = updatedAt ?? DateTime.now();

  factory SystemConfigModel.fromJson(Map<String, dynamic> json) {
    return SystemConfigModel(
      id: json['id'],
      defaultCommissionRate: json['defaultCommissionRate'].toDouble(),
      cancelPenaltyAmount: json['cancelPenaltyAmount'].toDouble(),
      noShowPenaltyAmount: json['noShowPenaltyAmount'].toDouble(),
      latePickupPenaltyAmount: json['latePickupPenaltyAmount'].toDouble(),
      autoRefreshInterval: json['autoRefreshInterval'],
      maxDistanceKm: json['maxDistanceKm'].toDouble(),
      maxBookingHours: json['maxBookingHours'],
      enableAutoAssignment: json['enableAutoAssignment'],
      enablePenalties: json['enablePenalties'],
      enableCommissions: json['enableCommissions'],
      updatedAt: DateTime.parse(json['updatedAt']),
      updatedBy: json['updatedBy'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'defaultCommissionRate': defaultCommissionRate,
      'cancelPenaltyAmount': cancelPenaltyAmount,
      'noShowPenaltyAmount': noShowPenaltyAmount,
      'latePickupPenaltyAmount': latePickupPenaltyAmount,
      'autoRefreshInterval': autoRefreshInterval,
      'maxDistanceKm': maxDistanceKm,
      'maxBookingHours': maxBookingHours,
      'enableAutoAssignment': enableAutoAssignment,
      'enablePenalties': enablePenalties,
      'enableCommissions': enableCommissions,
      'updatedAt': updatedAt.toIso8601String(),
      'updatedBy': updatedBy,
    };
  }

  SystemConfigModel copyWith({
    double? defaultCommissionRate,
    double? cancelPenaltyAmount,
    double? noShowPenaltyAmount,
    double? latePickupPenaltyAmount,
    int? autoRefreshInterval,
    double? maxDistanceKm,
    int? maxBookingHours,
    bool? enableAutoAssignment,
    bool? enablePenalties,
    bool? enableCommissions,
    DateTime? updatedAt,
    String? updatedBy,
  }) {
    return SystemConfigModel(
      id: id,
      defaultCommissionRate: defaultCommissionRate ?? this.defaultCommissionRate,
      cancelPenaltyAmount: cancelPenaltyAmount ?? this.cancelPenaltyAmount,
      noShowPenaltyAmount: noShowPenaltyAmount ?? this.noShowPenaltyAmount,
      latePickupPenaltyAmount: latePickupPenaltyAmount ?? this.latePickupPenaltyAmount,
      autoRefreshInterval: autoRefreshInterval ?? this.autoRefreshInterval,
      maxDistanceKm: maxDistanceKm ?? this.maxDistanceKm,
      maxBookingHours: maxBookingHours ?? this.maxBookingHours,
      enableAutoAssignment: enableAutoAssignment ?? this.enableAutoAssignment,
      enablePenalties: enablePenalties ?? this.enablePenalties,
      enableCommissions: enableCommissions ?? this.enableCommissions,
      updatedAt: updatedAt ?? DateTime.now(),
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }
}

class PenaltyModel {
  final String id;
  final String userId;
  final String bookingId;
  final String type; // cancel, no_show, late_pickup
  final double amount;
  final String reason;
  final String status; // pending, paid, waived
  final DateTime createdAt;
  final DateTime? paidAt;
  final String? paidBy;
  final UserModel? user;
  final BookingModel? booking;

  PenaltyModel({
    String? id,
    required this.userId,
    required this.bookingId,
    required this.type,
    required this.amount,
    required this.reason,
    this.status = 'pending',
    DateTime? createdAt,
    this.paidAt,
    this.paidBy,
    this.user,
    this.booking,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now();

  factory PenaltyModel.fromJson(Map<String, dynamic> json) {
    return PenaltyModel(
      id: json['id'],
      userId: json['userId'],
      bookingId: json['bookingId'],
      type: json['type'],
      amount: json['amount'].toDouble(),
      reason: json['reason'],
      status: json['status'],
      createdAt: DateTime.parse(json['createdAt']),
      paidAt: json['paidAt'] != null ? DateTime.parse(json['paidAt']) : null,
      paidBy: json['paidBy'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'bookingId': bookingId,
      'type': type,
      'amount': amount,
      'reason': reason,
      'status': status,
      'createdAt': createdAt.toIso8601String(),
      'paidAt': paidAt?.toIso8601String(),
      'paidBy': paidBy,
    };
  }

  bool get isPending => status == 'pending';
  bool get isPaid => status == 'paid';
  bool get isWaived => status == 'waived';
}

class ReportModel {
  final String id;
  final String type; // daily, weekly, monthly
  final DateTime startDate;
  final DateTime endDate;
  final int totalBookings;
  final int completedBookings;
  final int cancelledBookings;
  final double totalRevenue;
  final double totalCommissions;
  final double totalPenalties;
  final Map<String, int> bookingsByStatus;
  final Map<String, double> revenueByVehicleType;
  final List<UserStatsModel> topDrivers;
  final DateTime generatedAt;
  final String generatedBy;

  ReportModel({
    String? id,
    required this.type,
    required this.startDate,
    required this.endDate,
    this.totalBookings = 0,
    this.completedBookings = 0,
    this.cancelledBookings = 0,
    this.totalRevenue = 0.0,
    this.totalCommissions = 0.0,
    this.totalPenalties = 0.0,
    Map<String, int>? bookingsByStatus,
    Map<String, double>? revenueByVehicleType,
    List<UserStatsModel>? topDrivers,
    DateTime? generatedAt,
    required this.generatedBy,
  })  : id = id ?? const Uuid().v4(),
        bookingsByStatus = bookingsByStatus ?? {},
        revenueByVehicleType = revenueByVehicleType ?? {},
        topDrivers = topDrivers ?? [],
        generatedAt = generatedAt ?? DateTime.now();

  factory ReportModel.fromJson(Map<String, dynamic> json) {
    return ReportModel(
      id: json['id'],
      type: json['type'],
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      totalBookings: json['totalBookings'],
      completedBookings: json['completedBookings'],
      cancelledBookings: json['cancelledBookings'],
      totalRevenue: json['totalRevenue'].toDouble(),
      totalCommissions: json['totalCommissions'].toDouble(),
      totalPenalties: json['totalPenalties'].toDouble(),
      bookingsByStatus: Map<String, int>.from(json['bookingsByStatus']),
      revenueByVehicleType: Map<String, double>.from(json['revenueByVehicleType']),
      topDrivers: (json['topDrivers'] as List)
          .map((d) => UserStatsModel.fromJson(d))
          .toList(),
      generatedAt: DateTime.parse(json['generatedAt']),
      generatedBy: json['generatedBy'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'totalBookings': totalBookings,
      'completedBookings': completedBookings,
      'cancelledBookings': cancelledBookings,
      'totalRevenue': totalRevenue,
      'totalCommissions': totalCommissions,
      'totalPenalties': totalPenalties,
      'bookingsByStatus': bookingsByStatus,
      'revenueByVehicleType': revenueByVehicleType,
      'topDrivers': topDrivers.map((d) => d.toJson()).toList(),
      'generatedAt': generatedAt.toIso8601String(),
      'generatedBy': generatedBy,
    };
  }
}
