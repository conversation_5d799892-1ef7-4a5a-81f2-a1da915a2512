class User {
  final String id;
  final String email;
  final String name;
  final String? phone;
  final UserRole role;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? avatar;
  final Map<String, dynamic>? metadata;

  User({
    required this.id,
    required this.email,
    required this.name,
    this.phone,
    required this.role,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
    this.avatar,
    this.metadata,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      email: json['email'],
      name: json['name'],
      phone: json['phone'],
      role: UserRole.values.firstWhere(
        (e) => e.toString() == 'UserRole.${json['role']}',
        orElse: () => UserRole.driver,
      ),
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt'])
          : null,
      avatar: json['avatar'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'phone': phone,
      'role': role.toString().split('.').last,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'avatar': avatar,
      'metadata': metadata,
    };
  }

  User copyWith({
    String? id,
    String? email,
    String? name,
    String? phone,
    UserRole? role,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? avatar,
    Map<String, dynamic>? metadata,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      avatar: avatar ?? this.avatar,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, role: $role)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

enum UserRole {
  admin,
  driver,
  customer,
}

extension UserRoleExtension on UserRole {
  String get displayName {
    switch (this) {
      case UserRole.admin:
        return 'Quản trị viên';
      case UserRole.driver:
        return 'Tài xế';
      case UserRole.customer:
        return 'Khách hàng';
    }
  }

  String get description {
    switch (this) {
      case UserRole.admin:
        return 'Có quyền quản lý toàn bộ hệ thống';
      case UserRole.driver:
        return 'Có thể tạo và nhận cuốc xe';
      case UserRole.customer:
        return 'Có thể đặt xe và theo dõi chuyến đi';
    }
  }

  List<String> get permissions {
    switch (this) {
      case UserRole.admin:
        return [
          'manage_users',
          'manage_rides',
          'view_reports',
          'system_config',
          'manage_payments',
        ];
      case UserRole.driver:
        return [
          'create_ride',
          'accept_ride',
          'view_own_rides',
          'update_ride_status',
        ];
      case UserRole.customer:
        return [
          'book_ride',
          'view_own_bookings',
          'cancel_booking',
          'rate_driver',
        ];
    }
  }
}
