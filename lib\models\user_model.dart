import 'package:uuid/uuid.dart';

class UserModel {
  final String id;
  final String email;
  final String fullName;
  final String phoneNumber;
  final String role; // 'admin' or 'driver'
  final String? zaloId;
  final String? bankAccount;
  final String? bankName;
  final String? avatar;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final LocationModel? currentLocation;
  final UserStatsModel? stats;

  UserModel({
    String? id,
    required this.email,
    required this.fullName,
    required this.phoneNumber,
    required this.role,
    this.zaloId,
    this.bankAccount,
    this.bankName,
    this.avatar,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.currentLocation,
    this.stats,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      email: json['email'],
      fullName: json['fullName'],
      phoneNumber: json['phoneNumber'],
      role: json['role'],
      zaloId: json['zaloId'],
      bankAccount: json['bankAccount'],
      bankName: json['bankName'],
      avatar: json['avatar'],
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      currentLocation: json['currentLocation'] != null
          ? LocationModel.fromJson(json['currentLocation'])
          : null,
      stats: json['stats'] != null
          ? UserStatsModel.fromJson(json['stats'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'fullName': fullName,
      'phoneNumber': phoneNumber,
      'role': role,
      'zaloId': zaloId,
      'bankAccount': bankAccount,
      'bankName': bankName,
      'avatar': avatar,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'currentLocation': currentLocation?.toJson(),
      'stats': stats?.toJson(),
    };
  }

  UserModel copyWith({
    String? email,
    String? fullName,
    String? phoneNumber,
    String? role,
    String? zaloId,
    String? bankAccount,
    String? bankName,
    String? avatar,
    bool? isActive,
    DateTime? updatedAt,
    LocationModel? currentLocation,
    UserStatsModel? stats,
  }) {
    return UserModel(
      id: id,
      email: email ?? this.email,
      fullName: fullName ?? this.fullName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      role: role ?? this.role,
      zaloId: zaloId ?? this.zaloId,
      bankAccount: bankAccount ?? this.bankAccount,
      bankName: bankName ?? this.bankName,
      avatar: avatar ?? this.avatar,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      currentLocation: currentLocation ?? this.currentLocation,
      stats: stats ?? this.stats,
    );
  }

  bool get isAdmin => role == 'admin';
  bool get isDriver => role == 'driver';
}

class LocationModel {
  final double latitude;
  final double longitude;
  final String? address;
  final String? province;
  final String? district;
  final String? ward;
  final DateTime timestamp;

  LocationModel({
    required this.latitude,
    required this.longitude,
    this.address,
    this.province,
    this.district,
    this.ward,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      address: json['address'],
      province: json['province'],
      district: json['district'],
      ward: json['ward'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'province': province,
      'district': district,
      'ward': ward,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

class UserStatsModel {
  final int totalBookingsCreated;
  final int totalBookingsAccepted;
  final int totalBookingsCompleted;
  final int totalBookingsCancelled;
  final double totalEarnings;
  final double totalCommissions;
  final double totalPenalties;
  final double averageRating;
  final int totalRatings;

  UserStatsModel({
    this.totalBookingsCreated = 0,
    this.totalBookingsAccepted = 0,
    this.totalBookingsCompleted = 0,
    this.totalBookingsCancelled = 0,
    this.totalEarnings = 0.0,
    this.totalCommissions = 0.0,
    this.totalPenalties = 0.0,
    this.averageRating = 0.0,
    this.totalRatings = 0,
  });

  factory UserStatsModel.fromJson(Map<String, dynamic> json) {
    return UserStatsModel(
      totalBookingsCreated: json['totalBookingsCreated'] ?? 0,
      totalBookingsAccepted: json['totalBookingsAccepted'] ?? 0,
      totalBookingsCompleted: json['totalBookingsCompleted'] ?? 0,
      totalBookingsCancelled: json['totalBookingsCancelled'] ?? 0,
      totalEarnings: (json['totalEarnings'] ?? 0).toDouble(),
      totalCommissions: (json['totalCommissions'] ?? 0).toDouble(),
      totalPenalties: (json['totalPenalties'] ?? 0).toDouble(),
      averageRating: (json['averageRating'] ?? 0).toDouble(),
      totalRatings: json['totalRatings'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalBookingsCreated': totalBookingsCreated,
      'totalBookingsAccepted': totalBookingsAccepted,
      'totalBookingsCompleted': totalBookingsCompleted,
      'totalBookingsCancelled': totalBookingsCancelled,
      'totalEarnings': totalEarnings,
      'totalCommissions': totalCommissions,
      'totalPenalties': totalPenalties,
      'averageRating': averageRating,
      'totalRatings': totalRatings,
    };
  }
}
