import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:geolocator/geolocator.dart';
import '../core/app_config.dart';

class GoogleMapsService {
  static const String _baseUrl = 'https://maps.googleapis.com/maps/api';
  
  // Get place suggestions from Google Places API
  static Future<List<Map<String, dynamic>>> getPlaceSuggestions(String input) async {
    if (input.isEmpty) return [];
    
    try {
      final url = Uri.parse(
        '$_baseUrl/place/autocomplete/json'
        '?input=$input'
        '&key=${AppConfig.googleMapsApiKey}'
        '&components=country:vn'
        '&language=vi'
        '&types=geocode'
      );
      
      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 'OK') {
          return List<Map<String, dynamic>>.from(
            data['predictions'].map((prediction) => {
              'place_id': prediction['place_id'],
              'description': prediction['description'],
              'main_text': prediction['structured_formatting']['main_text'],
              'secondary_text': prediction['structured_formatting']['secondary_text'] ?? '',
            })
          );
        }
      }
    } catch (e) {
      print('Error getting place suggestions: $e');
    }
    
    return [];
  }
  
  // Get place details including coordinates
  static Future<Map<String, dynamic>?> getPlaceDetails(String placeId) async {
    try {
      final url = Uri.parse(
        '$_baseUrl/place/details/json'
        '?place_id=$placeId'
        '&key=${AppConfig.googleMapsApiKey}'
        '&fields=geometry,formatted_address,name'
      );
      
      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 'OK') {
          final result = data['result'];
          final location = result['geometry']['location'];
          
          return {
            'lat': location['lat'],
            'lng': location['lng'],
            'formatted_address': result['formatted_address'],
            'name': result['name'],
          };
        }
      }
    } catch (e) {
      print('Error getting place details: $e');
    }
    
    return null;
  }
  
  // Get directions between two points
  static Future<Map<String, dynamic>?> getDirections({
    required double originLat,
    required double originLng,
    required double destLat,
    required double destLng,
  }) async {
    try {
      final url = Uri.parse(
        '$_baseUrl/directions/json'
        '?origin=$originLat,$originLng'
        '&destination=$destLat,$destLng'
        '&key=${AppConfig.googleMapsApiKey}'
        '&language=vi'
        '&region=vn'
      );
      
      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 'OK' && data['routes'].isNotEmpty) {
          final route = data['routes'][0];
          final leg = route['legs'][0];
          
          return {
            'distance': leg['distance']['text'],
            'duration': leg['duration']['text'],
            'distance_value': leg['distance']['value'], // in meters
            'duration_value': leg['duration']['value'], // in seconds
            'polyline': route['overview_polyline']['points'],
            'start_address': leg['start_address'],
            'end_address': leg['end_address'],
          };
        }
      }
    } catch (e) {
      print('Error getting directions: $e');
    }
    
    return null;
  }
  
  // Get current location
  static Future<Position?> getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return null;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        return null;
      }

      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
    } catch (e) {
      print('Error getting current location: $e');
      return null;
    }
  }
  
  // Reverse geocoding - get address from coordinates
  static Future<String?> getAddressFromCoordinates(double lat, double lng) async {
    try {
      final url = Uri.parse(
        '$_baseUrl/geocode/json'
        '?latlng=$lat,$lng'
        '&key=${AppConfig.googleMapsApiKey}'
        '&language=vi'
        '&region=vn'
      );
      
      final response = await http.get(url);
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['status'] == 'OK' && data['results'].isNotEmpty) {
          return data['results'][0]['formatted_address'];
        }
      }
    } catch (e) {
      print('Error getting address from coordinates: $e');
    }
    
    return null;
  }
  
  // Calculate distance between two points
  static double calculateDistance(
    double startLat, double startLng,
    double endLat, double endLng,
  ) {
    return Geolocator.distanceBetween(startLat, startLng, endLat, endLng);
  }
  
  // Format distance for display
  static String formatDistance(double distanceInMeters) {
    if (distanceInMeters < 1000) {
      return '${distanceInMeters.round()}m';
    } else {
      return '${(distanceInMeters / 1000).toStringAsFixed(1)}km';
    }
  }
  
  // Format duration for display
  static String formatDuration(int durationInSeconds) {
    final hours = durationInSeconds ~/ 3600;
    final minutes = (durationInSeconds % 3600) ~/ 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
  
  // Get static map URL for preview
  static String getStaticMapUrl({
    required double lat,
    required double lng,
    int zoom = 15,
    int width = 400,
    int height = 300,
    String mapType = 'roadmap',
  }) {
    return '$_baseUrl/staticmap'
        '?center=$lat,$lng'
        '&zoom=$zoom'
        '&size=${width}x$height'
        '&maptype=$mapType'
        '&markers=color:red%7C$lat,$lng'
        '&key=${AppConfig.googleMapsApiKey}';
  }
  
  // Get route static map URL
  static String getRouteStaticMapUrl({
    required double originLat,
    required double originLng,
    required double destLat,
    required double destLng,
    int width = 400,
    int height = 300,
  }) {
    return '$_baseUrl/staticmap'
        '?size=${width}x$height'
        '&markers=color:green%7Clabel:A%7C$originLat,$originLng'
        '&markers=color:red%7Clabel:B%7C$destLat,$destLng'
        '&path=color:0x0000ff%7Cweight:3%7C$originLat,$originLng%7C$destLat,$destLng'
        '&key=${AppConfig.googleMapsApiKey}';
  }
}
