import 'dart:math';
import '../models/ride.dart';
import '../models/user.dart';

class MockDataService {
  static final Random _random = Random();
  
  // Mock locations in Ho Chi Minh City
  static final List<Map<String, dynamic>> _locations = [
    {'name': 'Quận 1, TP.HCM', 'lat': 10.7769, 'lng': 106.7009},
    {'name': 'Quận 3, TP.HCM', 'lat': 10.7756, 'lng': 106.6878},
    {'name': 'Quận 5, TP.HCM', 'lat': 10.7594, 'lng': 106.6672},
    {'name': 'Quận 7, TP.HCM', 'lat': 10.7378, 'lng': 106.7197},
    {'name': 'Quận 9, TP.HCM', 'lat': 10.8411, 'lng': 106.8098},
    {'name': 'Quận 10, TP.HCM', 'lat': 10.7727, 'lng': 106.6685},
    {'name': '<PERSON>uận 12, TP.HCM', 'lat': 10.8537, 'lng': 106.6291},
    {'name': '<PERSON><PERSON><PERSON>, TP.HCM', 'lat': 10.8014, 'lng': 106.7109},
    {'name': 'Gò Vấp, TP.HCM', 'lat': 10.8376, 'lng': 106.6765},
    {'name': 'Phú Nhuận, TP.HCM', 'lat': 10.7980, 'lng': 106.6947},
    {'name': 'Tân Bình, TP.HCM', 'lat': 10.8006, 'lng': 106.6530},
    {'name': 'Tân Phú, TP.HCM', 'lat': 10.7881, 'lng': 106.6256},
    {'name': 'Thủ Đức, TP.HCM', 'lat': 10.8502, 'lng': 106.7717},
    {'name': 'Bình Dương', 'lat': 10.9804, 'lng': 106.6519},
    {'name': 'Đồng Nai', 'lat': 10.9465, 'lng': 107.1676},
    {'name': 'Vũng Tàu', 'lat': 10.3460, 'lng': 107.0843},
    {'name': 'Cần Thơ', 'lat': 10.0452, 'lng': 105.7469},
    {'name': 'Đà Lạt', 'lat': 11.9404, 'lng': 108.4583},
    {'name': 'Nha Trang', 'lat': 12.2388, 'lng': 109.1967},
    {'name': 'Hà Nội', 'lat': 21.0285, 'lng': 105.8542},
  ];

  static final List<String> _driverNames = [
    'Nguyễn Văn An', 'Trần Thị Bình', 'Lê Minh Cường', 'Phạm Thu Hương',
    'Hoàng Đức Nam', 'Võ Thị Mai', 'Đặng Văn Tùng', 'Bùi Thu Linh',
    'Demo Driver', 'Đinh Văn Tùng', 'Lê Minh Cường', 'Phạm Thu Hương'
  ];

  static final List<String> _phoneNumbers = [
    'khach015', 'khach001', 'khach013', '0907654321', '0912345678',
    '0987654321', '0901234567', '0965432109', '0934567890', '0923456789'
  ];

  static List<Ride> generateMockRides({int count = 20}) {
    final List<Ride> rides = [];
    final now = DateTime.now();

    for (int i = 0; i < count; i++) {
      final fromLocation = _locations[_random.nextInt(_locations.length)];
      final toLocation = _locations[_random.nextInt(_locations.length)];
      
      // Ensure different locations
      while (toLocation['name'] == fromLocation['name']) {
        final newTo = _locations[_random.nextInt(_locations.length)];
        toLocation['name'] = newTo['name'];
        toLocation['lat'] = newTo['lat'];
        toLocation['lng'] = newTo['lng'];
      }

      final departureTime = now.add(Duration(
        hours: _random.nextInt(48) - 24, // -24 to +24 hours
        minutes: _random.nextInt(60),
      ));

      final seats = [4, 7, 9, 16][_random.nextInt(4)];
      final basePrice = _random.nextInt(300000) + 100000; // 100k - 400k VND
      final commission = (basePrice * 0.05).round(); // 5% commission

      final statuses = [RideStatus.available, RideStatus.inProgress, RideStatus.completed, RideStatus.cancelled];
      final status = statuses[_random.nextInt(statuses.length)];

      rides.add(Ride(
        id: 'ride_${i + 1}',
        driverId: 'driver_${_random.nextInt(5) + 1}',
        driverName: _driverNames[_random.nextInt(_driverNames.length)],
        driverPhone: _phoneNumbers[_random.nextInt(_phoneNumbers.length)],
        fromLocation: fromLocation['name'],
        toLocation: toLocation['name'],
        fromLatitude: fromLocation['lat'],
        fromLongitude: fromLocation['lng'],
        toLatitude: toLocation['lat'],
        toLongitude: toLocation['lng'],
        departureTime: departureTime,
        totalSeats: seats,
        availableSeats: status == RideStatus.available ? _random.nextInt(seats) + 1 : 0,
        pricePerSeat: basePrice.toDouble(),
        commission: commission.toDouble(),
        status: status,
        createdAt: departureTime.subtract(Duration(hours: _random.nextInt(72))),
        updatedAt: DateTime.now().subtract(Duration(minutes: _random.nextInt(60))),
        pickupAddress: _generateAddress(fromLocation['name']),
        dropoffAddress: _generateAddress(toLocation['name']),
        estimatedDuration: Duration(hours: _random.nextInt(8) + 1),
        notes: _random.nextBool() ? 'Đi ngay cảng sớm cảng tốt (10-20 phút)' : null,
      ));
    }

    // Sort by departure time
    rides.sort((a, b) => a.departureTime.compareTo(b.departureTime));
    return rides;
  }

  static String _generateAddress(String location) {
    final streetNumbers = ['123', '456', '789', '101', '202', '303'];
    final streets = [
      'Lê Lợi', 'Nguyễn Huệ', 'Đồng Khởi', 'Hai Bà Trưng', 'Pasteur',
      'Cách Mạng Tháng 8', 'Võ Văn Tần', 'Nam Kỳ Khởi Nghĩa'
    ];
    
    final streetNumber = streetNumbers[_random.nextInt(streetNumbers.length)];
    final street = streets[_random.nextInt(streets.length)];
    
    return '$streetNumber $street, $location';
  }

  static List<User> generateMockDrivers({int count = 10}) {
    final List<User> drivers = [];
    
    for (int i = 0; i < count; i++) {
      drivers.add(User(
        id: 'driver_${i + 1}',
        email: 'driver${i + 1}@example.com',
        name: _driverNames[i % _driverNames.length],
        phone: _phoneNumbers[i % _phoneNumbers.length],
        role: UserRole.driver,
        isActive: _random.nextBool(),
        createdAt: DateTime.now().subtract(Duration(days: _random.nextInt(365))),
      ));
    }
    
    return drivers;
  }

  static Map<String, dynamic> generateDashboardStats() {
    return {
      'totalRides': _random.nextInt(100) + 50,
      'activeDrivers': _random.nextInt(20) + 10,
      'todayRevenue': (_random.nextInt(5000000) + 1000000).toDouble(), // 1M - 6M VND
      'todayCommission': (_random.nextInt(500000) + 100000).toDouble(), // 100k - 600k VND
      'ridesInProgress': _random.nextInt(15) + 5,
      'completedToday': _random.nextInt(30) + 10,
      'cancelledToday': _random.nextInt(5),
    };
  }

  // Real-time update simulation
  static Stream<List<Ride>> getRidesStream() async* {
    while (true) {
      await Future.delayed(Duration(seconds: 5));
      yield generateMockRides(count: 15);
    }
  }

  static Stream<Map<String, dynamic>> getDashboardStatsStream() async* {
    while (true) {
      await Future.delayed(Duration(seconds: 10));
      yield generateDashboardStats();
    }
  }

  // Location suggestions for autocomplete
  static List<String> getLocationSuggestions(String query) {
    if (query.isEmpty) return [];
    
    return _locations
        .map((loc) => loc['name'] as String)
        .where((name) => name.toLowerCase().contains(query.toLowerCase()))
        .take(5)
        .toList();
  }

  // Get coordinates for a location
  static Map<String, double>? getLocationCoordinates(String locationName) {
    final location = _locations.firstWhere(
      (loc) => loc['name'] == locationName,
      orElse: () => {},
    );
    
    if (location.isEmpty) return null;
    
    return {
      'lat': location['lat'],
      'lng': location['lng'],
    };
  }

  static List<Map<String, dynamic>> getAvailableRides() {
    final availableRides = [
      {
        'id': '1',
        'vehicleType': '7 seats',
        'pricePerSeat': 250.0,
        'commission': 12.5,
        'from': 'ĐH Kinh tế',
        'to': 'Biên Hòa',
        'departureTime': '15:03 - Hôm nay',
        'driverName': 'khach015',
        'status': 'pending',
      },
      {
        'id': '2',
        'vehicleType': '4 seats',
        'pricePerSeat': 350.0,
        'commission': 17.5,
        'from': 'Bến xe Miền Đông',
        'to': 'Sân bay Tân Sơn Nhất',
        'departureTime': '15:03 - Đi ngay',
        'driverName': 'khach001',
        'status': 'pending',
      },
      {
        'id': '3',
        'vehicleType': '7 seats',
        'pricePerSeat': 280.0,
        'commission': 14.0,
        'from': 'Quận 1',
        'to': 'Đồng Nai',
        'departureTime': '16:30 - Hôm nay',
        'driverName': 'khach025',
        'status': 'pending',
      },
      {
        'id': '4',
        'vehicleType': '4 seats',
        'pricePerSeat': 420.0,
        'commission': 21.0,
        'from': 'Quận 7',
        'to': 'Cần Thơ',
        'departureTime': '17:00 - Hôm nay',
        'driverName': 'khach030',
        'status': 'pending',
      },
      {
        'id': '5',
        'vehicleType': '16 seats',
        'pricePerSeat': 180.0,
        'commission': 9.0,
        'from': 'Bến Thành',
        'to': 'Mũi Né',
        'departureTime': '18:00 - Hôm nay',
        'driverName': 'khach020',
        'status': 'pending',
      },
      {
        'id': '6',
        'vehicleType': '7 seats',
        'pricePerSeat': 320.0,
        'commission': 16.0,
        'from': 'Sân bay Tân Sơn Nhất',
        'to': 'Vũng Tàu',
        'departureTime': '19:15 - Hôm nay',
        'driverName': 'khach035',
        'status': 'pending',
      },
    ];

    return availableRides;
  }
}
