import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/ride.dart';
import 'mock_data_service.dart';

class RealTimeService extends ChangeNotifier {
  static final RealTimeService _instance = RealTimeService._internal();
  factory RealTimeService() => _instance;
  RealTimeService._internal();

  Timer? _ridesTimer;
  Timer? _statsTimer;
  
  List<Ride> _rides = [];
  Map<String, dynamic> _dashboardStats = {};
  
  bool _isRidesAutoUpdateEnabled = true;
  bool _isStatsAutoUpdateEnabled = true;

  List<Ride> get rides => _rides;
  Map<String, dynamic> get dashboardStats => _dashboardStats;
  bool get isRidesAutoUpdateEnabled => _isRidesAutoUpdateEnabled;
  bool get isStatsAutoUpdateEnabled => _isStatsAutoUpdateEnabled;

  // Stream controllers
  final StreamController<List<Ride>> _ridesController = StreamController<List<Ride>>.broadcast();
  final StreamController<Map<String, dynamic>> _statsController = StreamController<Map<String, dynamic>>.broadcast();

  Stream<List<Ride>> get ridesStream => _ridesController.stream;
  Stream<Map<String, dynamic>> get statsStream => _statsController.stream;

  void startRidesAutoUpdate() {
    if (_ridesTimer?.isActive == true) return;
    
    _isRidesAutoUpdateEnabled = true;
    
    // Initial load
    _updateRides();
    
    // Set up periodic updates every 5 seconds
    _ridesTimer = Timer.periodic(Duration(seconds: 5), (timer) {
      if (_isRidesAutoUpdateEnabled) {
        _updateRides();
      }
    });
    
    notifyListeners();
  }

  void stopRidesAutoUpdate() {
    _isRidesAutoUpdateEnabled = false;
    _ridesTimer?.cancel();
    notifyListeners();
  }

  void startStatsAutoUpdate() {
    if (_statsTimer?.isActive == true) return;
    
    _isStatsAutoUpdateEnabled = true;
    
    // Initial load
    _updateStats();
    
    // Set up periodic updates every 10 seconds
    _statsTimer = Timer.periodic(Duration(seconds: 10), (timer) {
      if (_isStatsAutoUpdateEnabled) {
        _updateStats();
      }
    });
    
    notifyListeners();
  }

  void stopStatsAutoUpdate() {
    _isStatsAutoUpdateEnabled = false;
    _statsTimer?.cancel();
    notifyListeners();
  }

  void _updateRides() {
    try {
      final newRides = MockDataService.generateMockRides(count: 15);
      
      // Smooth transition - only update if there are actual changes
      if (!_areRidesEqual(_rides, newRides)) {
        _rides = newRides;
        _ridesController.add(_rides);
        notifyListeners();
        
        if (kDebugMode) {
          print('Rides updated at ${DateTime.now()}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating rides: $e');
      }
    }
  }

  void _updateStats() {
    try {
      final newStats = MockDataService.generateDashboardStats();
      
      // Only update if there are significant changes
      if (!_areStatsEqual(_dashboardStats, newStats)) {
        _dashboardStats = newStats;
        _statsController.add(_dashboardStats);
        notifyListeners();
        
        if (kDebugMode) {
          print('Stats updated at ${DateTime.now()}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating stats: $e');
      }
    }
  }

  bool _areRidesEqual(List<Ride> oldRides, List<Ride> newRides) {
    if (oldRides.length != newRides.length) return false;
    
    for (int i = 0; i < oldRides.length; i++) {
      if (oldRides[i].id != newRides[i].id ||
          oldRides[i].status != newRides[i].status ||
          oldRides[i].availableSeats != newRides[i].availableSeats) {
        return false;
      }
    }
    return true;
  }

  bool _areStatsEqual(Map<String, dynamic> oldStats, Map<String, dynamic> newStats) {
    if (oldStats.isEmpty) return false;
    
    // Check key metrics for significant changes (>5% difference)
    final keys = ['totalRides', 'activeDrivers', 'todayRevenue', 'todayCommission'];
    
    for (String key in keys) {
      final oldValue = oldStats[key] ?? 0;
      final newValue = newStats[key] ?? 0;
      
      if (oldValue == 0 && newValue != 0) return false;
      if (oldValue != 0) {
        final percentChange = ((newValue - oldValue) / oldValue).abs();
        if (percentChange > 0.05) return false; // 5% threshold
      }
    }
    
    return true;
  }

  // Manual refresh methods
  Future<void> refreshRides() async {
    _updateRides();
  }

  Future<void> refreshStats() async {
    _updateStats();
  }

  // Toggle auto-update
  void toggleRidesAutoUpdate() {
    if (_isRidesAutoUpdateEnabled) {
      stopRidesAutoUpdate();
    } else {
      startRidesAutoUpdate();
    }
  }

  void toggleStatsAutoUpdate() {
    if (_isStatsAutoUpdateEnabled) {
      stopStatsAutoUpdate();
    } else {
      startStatsAutoUpdate();
    }
  }

  // Get specific ride by ID with real-time updates
  Stream<Ride?> getRideStream(String rideId) {
    return ridesStream.map((rides) {
      try {
        return rides.firstWhere((ride) => ride.id == rideId);
      } catch (e) {
        return null;
      }
    });
  }

  // Filter rides by status with real-time updates
  Stream<List<Ride>> getRidesByStatusStream(RideStatus status) {
    return ridesStream.map((rides) {
      return rides.where((ride) => ride.status == status).toList();
    });
  }

  // Get available rides only
  Stream<List<Ride>> getAvailableRidesStream() {
    return ridesStream.map((rides) {
      return rides.where((ride) => 
        ride.status == RideStatus.available && 
        ride.availableSeats > 0 &&
        ride.departureTime.isAfter(DateTime.now())
      ).toList();
    });
  }

  @override
  void dispose() {
    _ridesTimer?.cancel();
    _statsTimer?.cancel();
    _ridesController.close();
    _statsController.close();
    super.dispose();
  }
}
