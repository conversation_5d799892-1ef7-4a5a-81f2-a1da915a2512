import 'package:flutter/material.dart';
import '../services/google_maps_service.dart';
import '../services/mock_data_service.dart';

class LocationSearchField extends StatefulWidget {
  final TextEditingController controller;
  final String hintText;
  final IconData prefixIcon;
  final Function(String, double?, double?)? onLocationSelected;
  final String? Function(String?)? validator;

  const LocationSearchField({
    Key? key,
    required this.controller,
    required this.hintText,
    required this.prefixIcon,
    this.onLocationSelected,
    this.validator,
  }) : super(key: key);

  @override
  _LocationSearchFieldState createState() => _LocationSearchFieldState();
}

class _LocationSearchFieldState extends State<LocationSearchField> {
  List<Map<String, dynamic>> _suggestions = [];
  bool _isLoading = false;
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    _removeOverlay();
    super.dispose();
  }

  void _onTextChanged() {
    final text = widget.controller.text;
    if (text.length > 2) {
      _searchLocations(text);
    } else {
      _removeOverlay();
    }
  }

  Future<void> _searchLocations(String query) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Try Google Places API first, fallback to mock data
      List<Map<String, dynamic>> suggestions = [];
      
      try {
        suggestions = await GoogleMapsService.getPlaceSuggestions(query);
      } catch (e) {
        // Fallback to mock data
        final mockSuggestions = MockDataService.getLocationSuggestions(query);
        suggestions = mockSuggestions.map((name) => {
          'description': name,
          'main_text': name.split(',')[0],
          'secondary_text': name.contains(',') ? name.split(',').skip(1).join(',').trim() : '',
          'place_id': 'mock_${name.hashCode}',
        }).toList();
      }

      setState(() {
        _suggestions = suggestions;
        _isLoading = false;
      });

      if (suggestions.isNotEmpty) {
        _showOverlay();
      } else {
        _removeOverlay();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _suggestions = [];
      });
      _removeOverlay();
    }
  }

  void _showOverlay() {
    _removeOverlay();

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: MediaQuery.of(context).size.width - 32,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0, 60),
          child: Material(
            elevation: 8,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              constraints: BoxConstraints(maxHeight: 200),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: ListView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                itemCount: _suggestions.length,
                itemBuilder: (context, index) {
                  final suggestion = _suggestions[index];
                  return ListTile(
                    dense: true,
                    leading: Icon(
                      Icons.location_on,
                      color: Colors.grey.shade600,
                      size: 20,
                    ),
                    title: Text(
                      suggestion['main_text'] ?? suggestion['description'],
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    subtitle: suggestion['secondary_text']?.isNotEmpty == true
                        ? Text(
                            suggestion['secondary_text'],
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          )
                        : null,
                    onTap: () => _selectLocation(suggestion),
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );

    Overlay.of(context)?.insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  Future<void> _selectLocation(Map<String, dynamic> suggestion) async {
    _removeOverlay();
    
    final description = suggestion['description'] ?? suggestion['main_text'];
    widget.controller.text = description;

    double? lat, lng;

    // Get coordinates
    if (suggestion['place_id']?.startsWith('mock_') == true) {
      // Mock data
      final coords = MockDataService.getLocationCoordinates(description);
      lat = coords?['lat'];
      lng = coords?['lng'];
    } else {
      // Google Places API
      try {
        final details = await GoogleMapsService.getPlaceDetails(suggestion['place_id']);
        lat = details?['lat'];
        lng = details?['lng'];
      } catch (e) {
        print('Error getting place details: $e');
      }
    }

    if (widget.onLocationSelected != null) {
      widget.onLocationSelected!(description, lat, lng);
    }
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextFormField(
        controller: widget.controller,
        validator: widget.validator,
        decoration: InputDecoration(
          hintText: widget.hintText,
          prefixIcon: Icon(widget.prefixIcon),
          suffixIcon: _isLoading
              ? Padding(
                  padding: EdgeInsets.all(12),
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                    ),
                  ),
                )
              : widget.controller.text.isNotEmpty
                  ? IconButton(
                      icon: Icon(Icons.clear),
                      onPressed: () {
                        widget.controller.clear();
                        _removeOverlay();
                      },
                    )
                  : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Theme.of(context).primaryColor),
          ),
        ),
        onTap: () {
          if (widget.controller.text.length > 2 && _suggestions.isNotEmpty) {
            _showOverlay();
          }
        },
      ),
    );
  }
}
