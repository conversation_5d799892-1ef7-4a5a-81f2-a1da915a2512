import 'package:flutter/material.dart';
import '../services/real_time_service.dart';

class RealTimeStatusWidget extends StatefulWidget {
  final String updateText;
  final int updateIntervalSeconds;

  const RealTimeStatusWidget({
    Key? key,
    this.updateText = 'Cập nhật sau',
    this.updateIntervalSeconds = 5,
  }) : super(key: key);

  @override
  _RealTimeStatusWidgetState createState() => _RealTimeStatusWidgetState();
}

class _RealTimeStatusWidgetState extends State<RealTimeStatusWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  final RealTimeService _realTimeService = RealTimeService();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(seconds: 1),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    
    // Start animation loop
    _startAnimation();
  }

  void _startAnimation() {
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<dynamic>>(
      stream: _realTimeService.ridesStream,
      builder: (context, snapshot) {
        final isConnected = snapshot.hasData;
        final isLoading = snapshot.connectionState == ConnectionState.waiting;

        return AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getStatusColor(isConnected, isLoading).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _getStatusColor(isConnected, isLoading).withOpacity(0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  AnimatedContainer(
                    duration: Duration(milliseconds: 300),
                    child: Icon(
                      _getStatusIcon(isConnected, isLoading),
                      color: _getStatusColor(isConnected, isLoading),
                      size: 16,
                    ),
                  ),
                  SizedBox(width: 4),
                  Text(
                    _getStatusText(isConnected, isLoading),
                    style: TextStyle(
                      color: _getStatusColor(isConnected, isLoading),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Color _getStatusColor(bool isConnected, bool isLoading) {
    if (isLoading) return Colors.orange;
    return isConnected ? Colors.green : Colors.red;
  }

  IconData _getStatusIcon(bool isConnected, bool isLoading) {
    if (isLoading) return Icons.sync;
    return isConnected ? Icons.wifi : Icons.wifi_off;
  }

  String _getStatusText(bool isConnected, bool isLoading) {
    if (isLoading) return 'Đang kết nối...';
    if (isConnected) {
      return '${widget.updateText} ${widget.updateIntervalSeconds}s';
    }
    return 'Mất kết nối';
  }
}

class RealTimeIndicator extends StatefulWidget {
  final bool isActive;
  final String label;
  final VoidCallback? onTap;

  const RealTimeIndicator({
    Key? key,
    required this.isActive,
    required this.label,
    this.onTap,
  }) : super(key: key);

  @override
  _RealTimeIndicatorState createState() => _RealTimeIndicatorState();
}

class _RealTimeIndicatorState extends State<RealTimeIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );

    if (widget.isActive) {
      _controller.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(RealTimeIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive != oldWidget.isActive) {
      if (widget.isActive) {
        _controller.repeat(reverse: true);
      } else {
        _controller.stop();
        _controller.reset();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _pulseAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: widget.isActive ? _pulseAnimation.value : 1.0,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: widget.isActive ? Colors.green.shade50 : Colors.grey.shade100,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: widget.isActive ? Colors.green : Colors.grey.shade300,
                  width: 1.5,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: widget.isActive ? Colors.green : Colors.grey,
                      shape: BoxShape.circle,
                    ),
                  ),
                  SizedBox(width: 6),
                  Text(
                    widget.label,
                    style: TextStyle(
                      color: widget.isActive ? Colors.green.shade700 : Colors.grey.shade600,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

class ConnectionStatusBar extends StatelessWidget {
  final bool isConnected;
  final String? message;
  final VoidCallback? onRetry;

  const ConnectionStatusBar({
    Key? key,
    required this.isConnected,
    this.message,
    this.onRetry,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isConnected) return SizedBox.shrink();

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      color: Colors.red.shade100,
      child: Row(
        children: [
          Icon(Icons.warning, color: Colors.red.shade700, size: 16),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              message ?? 'Mất kết nối với máy chủ',
              style: TextStyle(
                color: Colors.red.shade700,
                fontSize: 14,
              ),
            ),
          ),
          if (onRetry != null)
            TextButton(
              onPressed: onRetry,
              child: Text(
                'Thử lại',
                style: TextStyle(
                  color: Colors.red.shade700,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class AutoRefreshToggle extends StatelessWidget {
  final bool isEnabled;
  final ValueChanged<bool> onChanged;
  final String label;

  const AutoRefreshToggle({
    Key? key,
    required this.isEnabled,
    required this.onChanged,
    this.label = 'Tự động cập nhật',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isEnabled ? Icons.autorenew : Icons.pause,
            color: isEnabled ? Colors.green : Colors.grey,
            size: 16,
          ),
          SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(
              color: isEnabled ? Colors.green : Colors.grey,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(width: 8),
          Switch(
            value: isEnabled,
            onChanged: onChanged,
            activeColor: Colors.green,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        ],
      ),
    );
  }
}
